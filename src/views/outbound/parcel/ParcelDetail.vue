<script setup>
import { ref, onMounted } from 'vue'
import { detail, detailLogs, appendixPreview } from '@/api/outbound/parcel'
import { ElMessage } from 'element-plus'
import { Download, Paperclip, Printer, DocumentCopy } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const outboundForm = ref([])
const wmsAppendixList = ref([])
const logList = ref([])

const customerCode = ref(null)
const deliveryNo = ref(null)
const whCode = ref(null)
const sourceNo = ref(null)
const activeName = ref('sku')

const logTotal = ref(0)
const logCurrentPage = ref(1)
const logPageSize = ref(20)

const loading = ref(false)

const handleSizeChange = (size) => {
  logPageSize.value = size
  logInfo()
}

const handleCurrentChange = (page) => {
  logCurrentPage.value = page
  logInfo()
}

const parcelInfo = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      customerCode: customerCode.value,
      deliveryNo: deliveryNo.value,
      whCode: whCode.value,
    }

    const response = await detail(params)

    if (response && response.data) {
      outboundForm.value = response.data || []
      wmsAppendixList.value = response.data.wmsAppendixList || []
    }
  } catch (error) {
    console.error('获取列表失败', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

const logInfo = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      current: logCurrentPage.value,
      size: logPageSize.value,
      bizNo: deliveryNo.value,
      customerCode: customerCode.value,
      whCode: whCode.value,
    }

    const response = await detailLogs(params)

    if (response && response.data) {
      logList.value = response.data.records || []
      logTotal.value = response.data.total || 0
      logCurrentPage.value = response.data.current || 1
      logPageSize.value = response.data.size || 20
    }
  } catch (error) {
    console.error('获取列表失败', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

//复制
const copy = async (val) => {
  await navigator.clipboard.writeText(val)
  ElMessage.success('复制成功')
}

//预览/下载
const preview = async (fileKey, fileName, type = 0) => {
  try {
    if (!fileKey || !fileName) {
      return
    }

    // 构建查询参数
    const params = {
      fileKey: fileKey,
      fileName: fileName,
      customerCode: customerCode.value,
      whCode: whCode.value,
    }

    const response = await appendixPreview(params)

    if (response && response.data) {
      if (type == 0) {
        //预览
        window.open(response.data.previewUrl, '_blank')
      }
      if (type == 1) {
        //下载
        window.open(response.data.downLoadUrl)
      }
      if (type == 2) {
        //打印
        // var printWindow = window.open(response.data.previewUrl);
        //printWindow.document.write('<html><head><title>Print Content</title></head><body>' + content + '</body></html>');
        // printWindow.document.close();
        // printWindow.print();

        window.print()
      }
    }
  } catch (error) {
    console.error('获取数据失败', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  // console.log(router.currentRoute.value.params)
  // console.log(router.currentRoute.value.query)

  deliveryNo.value = router.currentRoute.value.query.deliveryNo
  customerCode.value = router.currentRoute.value.query.customerCode
  whCode.value = router.currentRoute.value.query.whCode
  sourceNo.value = router.currentRoute.value.query.sourceNo

  parcelInfo()
  logInfo()
})
</script>

<template>
  <div class="profile-container">
    <el-form label-width="100px" label-position="left">
      <el-card class="profile-card" shadow="hover" v-loading="loading">
        <el-row>
          <el-col :span="12">
            <span style="margin-left: 0px">
              <label style="font-size: 24px; font-weight: bold">{{ outboundForm.sourceNo }}</label>
              <el-link
                type="primary"
                style="margin-left: 5px; margin-bottom: 15px"
                :icon="DocumentCopy"
                @click="copy(outboundForm.sourceNo)"
              >
              </el-link>
              <label style="font-size: 14px; margin-left: 10px; color: slategray" type="info">
                {{ outboundForm.statusName }}
              </label>
            </span>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="profile-card" shadow="hover" v-loading="loading" style="margin-top: 10px">
        <span style="font-size: 20px; font-weight: bold; color: #0071e3">丨</span>
        <span style="padding-bottom: 20px; font-size: 16px; font-weight: bolder">基础信息</span>
        <el-row style="margin-top: 20px">
          <el-col :span="8">
            <el-form-item label="客户:" prop="">
              <span style="" class="type-name" v-if="outboundForm.customerName">
                {{ outboundForm.customerName }} ({{ outboundForm.customerCode }})</span
              >
              <span v-else>-</span>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="参考单号:" prop="">
              <span style="" class="type-name">{{
                outboundForm.referOrderNo ? outboundForm.referOrderNo : '-'
              }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="平台单号:" prop="">
              <span style="" class="type-name">{{
                outboundForm.platformOrderNo ? outboundForm.platformOrderNo : '-'
              }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="物流渠道:" prop="">
              <span class="type-name" v-if="outboundForm.logisticsChannelName">
                {{ outboundForm.logisticsChannelName }} ({{ outboundForm.logisticsChannel }})
              </span>
              <span class="type-name" v-else>-</span>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="订单品种:" prop="">
              <span style="" class="type-name">{{
                outboundForm.varietyTypeName ? outboundForm.varietyTypeName : '-'
              }}</span>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="签名类型:" prop="">
              <span style="" class="type-name">{{
                outboundForm.signatureService ? outboundForm.signatureService : '-'
              }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="包裹数量:" prop="">
              <span style="" class="type-name">{{ outboundForm.packageNum }}</span>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="SKU品种数:" prop="">
              <span style="" class="type-name">{{ outboundForm.skuKind }}</span>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="保险服务:" prop="">
              <span style="" class="type-name">{{
                outboundForm.insureMoney ? outboundForm.insureMoney : '-'
              }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="总数量:" prop="">
              <span style="" class="type-name">{{ outboundForm.skuAmount }}</span>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="备注:" prop="">
              <span style="" class="type-name">{{
                outboundForm.remark ? outboundForm.remark : '-'
              }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card style="margin-top: 10px">
        <span style="font-size: 20px; font-weight: bold; color: #0071e3">丨</span>
        <span style="padding-bottom: 20px; font-size: 16px; font-weight: bolder">收件信息</span>
        <el-row style="margin-top: 20px">
          <el-col :span="8">
            <el-form-item label="收件人:" prop="">
              <span style="" class="type-name">{{
                outboundForm.receiver ? outboundForm.receiver : '-'
              }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="电话:" prop="">
              <span style="" class="type-name">{{
                outboundForm.telephone ? outboundForm.telephone : '-'
              }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="邮箱:" prop="">
              <span style="" class="type-name">{{
                outboundForm.email ? outboundForm.email : '-'
              }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="收件人税号:" prop="">
              <span style="" class="type-name">{{
                outboundForm.taxNum ? outboundForm.taxNum : '-'
              }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="公司名称:" prop="">
              <span style="" class="type-name">{{
                outboundForm.companyName ? outboundForm.companyName : '-'
              }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="国家/地区:" prop="">
              <span style="" class="type-name">
                {{ outboundForm.countryRegionName ? outboundForm.countryRegionName : '-' }}
              </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="省/州名称:" prop="">
              <span style="" class="type-name">
                {{ outboundForm.provinceName ? outboundForm.provinceName : '-' }}
                ({{ outboundForm.provinceCode ? outboundForm.provinceCode : '-' }})
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="城市名称:" prop="">
              <span style="" class="type-name">
                {{ outboundForm.cityName ? outboundForm.cityName : '-' }}
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="邮编:" prop="">
              <span style="" class="type-name">
                {{ outboundForm.postCode ? outboundForm.postCode : '-' }}
              </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="门牌号:" prop="">
              <span style="" class="type-name">
                {{ outboundForm.houseNum ? outboundForm.houseNum : '-' }}
              </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="地址1:" prop="">
              <span style="" class="type-name">
                {{ outboundForm.addressOne ? outboundForm.addressOne : '-' }}
              </span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="地址2:" prop="">
              <span style="" class="type-name">
                {{ outboundForm.addressTwo ? outboundForm.addressTwo : '-' }}
              </span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card style="margin-top: 10px">
        <div class="app-container">
          <span style="font-size: 20px; font-weight: bold; color: #0071e3">丨</span>
          <span style="font-size: 16px; font-weight: bolder">物流包裹</span>
          <br />
          <el-row style="margin-top: 20px; margin-left: 15px; font-size: 14px; color: slategray">
            物流承运商：{{ outboundForm.logisticsCarrier ? outboundForm.logisticsCarrier : '-' }}
          </el-row>
          <el-table :data="outboundForm.packageList" style="width: 100%; margin-top: 20px">
            <el-table-column label="包裹序号" prop="wmsPackSerial" width="100" />
            <el-table-column label="物流跟踪号" width="180px;" prop="expressNo">
              <template #default="scope">
                <span v-if="scope.row.expressNo">
                  {{ scope.row.expressNo }}
                  <el-link
                    type="primary"
                    style="margin-left: 5px; margin-bottom: 15px"
                    :icon="DocumentCopy"
                    @click="copy(scope.row.expressNo)"
                  >
                  </el-link>
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column
              label="包裹尺寸(长*宽*高)"
              prop="skuCount"
              width="160px"
              align="center"
            >
              <template #default="scope">
                {{ scope.row.length }} * {{ scope.row.width }} * {{ scope.row.height }}
                {{ scope.row.sizeUnit }}
              </template>
            </el-table-column>
            <el-table-column label="包材编码" prop="" width="100px;">
              <template #default="scope">
                <span v-if="scope.row.packList.length < 2" v-for="pack in scope.row.packList">
                  <el-tag type="primary">{{ pack.packNo }} * {{ pack.qty }}</el-tag>
                </span>
                <el-popover v-else effect="light" trigger="hover" placement="top" width="auto">
                  <template #default>
                    <el-table :data="scope.row.packList">
                      <el-table-column label="编码" prop="packNo" width="200" />
                      <el-table-column label="数量" prop="qty" width="100" />
                    </el-table>
                  </template>
                  <template #reference>
                    <el-tag>多个({{ scope.row.packList.length }})</el-tag>
                  </template>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column label="包材重量" prop="" width="100px;" align="center">
              <template #default="scope">
                <span v-if="scope.row.packList.length < 2" v-for="pack in scope.row.packList">
                  {{ pack.wmsWeight }} * {{ pack.qty }}
                </span>
                <el-popover v-else effect="light" trigger="hover" placement="top" width="auto">
                  <template #default>
                    <el-table :data="scope.row.packList">
                      <el-table-column label="重量" prop="wmsWeight" width="200" />
                      <el-table-column label="数量" prop="qty" width="100" />
                    </el-table>
                  </template>
                  <template #reference>
                    <el-tag>多个({{ scope.row.packList.length }})</el-tag>
                  </template>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column label="包裹重量" prop="" width="100" align="center">
              <template #default="scope">
                <span v-if="scope.row.realWeight"
                  >{{ scope.row.realWeight }} {{ scope.row.weightUnit }}</span
                >
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="SKU * 数量" align="center" width="220px" show-overflow-tooltip>
              <template #default="scope">
                <span v-if="scope.row.skuList.length < 2" v-for="sku in scope.row.skuList"
                  >{{ sku.productSku }} * {{ sku.deliveryQty }}
                </span>
                <el-popover v-else effect="light" trigger="hover" placement="top" width="auto">
                  <template #default>
                    <el-table :data="scope.row.skuList">
                      <el-table-column label="SKU" prop="productSku" width="200" />
                      <el-table-column label="数量" prop="deliveryQty" width="100" />
                    </el-table>
                  </template>
                  <template #reference>
                    <el-tag>多个({{ scope.row.skuList.length }})</el-tag>
                  </template>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column
              label="面单打印"
              prop="expressPrintStatusName"
              width="120px;"
              align="center"
            />
            <el-table-column label="状态" prop="" width="180px;" align="center">
              <template #default="scope">
                <span v-if="scope.row.reviewStatus == 0">未复核</span>
              </template>
            </el-table-column>
            <el-table-column label="物流面单" prop="" width="180px;" align="center">
              <template #default="scope">
                <span v-if="scope.row.fileKey && scope.row.fileName">
                  <el-link
                    type="primary"
                    :icon="Paperclip"
                    @click="preview(scope.row.fileKey, scope.row.fileName, 0)"
                  >
                    {{ scope.row.fileName }}
                  </el-link>
                  <el-link
                    :icon="Download"
                    @click="preview(scope.row.fileKey, scope.row.fileName, 1)"
                  >
                  </el-link>
                  <el-link
                    style="margin-left: 10px"
                    :icon="Printer"
                    @click="preview(scope.row.fileKey, scope.row.fileName, 2)"
                  >
                  </el-link>
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
      <el-card style="margin-top: 10px">
        <div class="app-container">
          <el-tabs v-model="activeName">
            <el-tab-pane label="产品明细" name="sku">
              <el-table :data="outboundForm.skuVOList" show-summary>
                <el-table-column label="SKU" prop="productSku" width="250px" fixed="left">
                  <template #default="scope">
                    {{ scope.row.productSku }}
                    <el-link
                      type="primary"
                      style="margin-left: 5px; margin-bottom: 15px"
                      :icon="DocumentCopy"
                      @click="copy(scope.row.productSku)"
                    >
                    </el-link>
                  </template>
                </el-table-column>
                <el-table-column
                  label="产品名称"
                  prop="productName"
                  style="width: 180px"
                  show-overflow-tooltip
                />
                <el-table-column label="出库数量" prop="actualDeliveryQty" align="center" />
                <el-table-column
                  label="申报价格"
                  prop="declarePrice"
                  style="width: 150px"
                  align="center"
                >
                  <template #default="scope">
                    {{ scope.row.declarePrice }} {{ scope.row.currencyCode }}
                  </template>
                </el-table-column>
                <el-table-column label="是否需要包材" prop="" align="center">
                  <template #default="scope">
                    <label v-if="scope.row.needPack == 1">是</label>
                    <label v-else>否</label>
                  </template>
                </el-table-column>
                <el-table-column label="推荐包材编码" prop="" width="150px;" align="center">
                  <template #default="scope">
                    <span v-if="scope.row.recommendedPackMaterial.length">
                      <label v-for="dict in scope.row.recommendedPackMaterial">{{
                        dict.code
                      }}</label>
                    </span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column label="推荐包材名称" prop="" align="center" width="150px">
                  <template #default="scope">
                    <span v-if="scope.row.recommendedPackMaterial.length">
                      <label v-for="dict in scope.row.recommendedPackMaterial">{{
                        dict.name
                      }}</label>
                    </span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="增值服务" name="add">
              <el-table :data="outboundForm.vasList" style="width: 100%">
                <el-table-column label="增值服务" prop="" />
                <el-table-column label="计费变量" prop="" />
                <el-table-column label="数量" prop="counts" />
                <el-table-column label="客户备注" prop="consumerRemarks"> </el-table-column>
                <el-table-column label="备注" prop="remarks" />
              </el-table>
            </el-tab-pane>
            <el-tab-pane :label="'附件(' + wmsAppendixList.length + ')'" name="att">
              <el-table :data="wmsAppendixList" style="width: 100%">
                <el-table-column label="文件名称" prop="fileName" align="center" />
                <el-table-column label="附件来源" prop="" align="center" />
                <el-table-column label="附件类型" prop="bizAppendixTypeName" align="center" />
                <el-table-column label="文件大小" prop="" align="center">
                  <template #default="scope">
                    {{ (scope.row.fileSize / 1024).toFixed(2) }} KB
                  </template>
                </el-table-column>
                <el-table-column label="上传时间" prop="uploadTime" align="center" />
                <el-table-column label="操作" prop="" align="center">
                  <template #default="scope">
                    <el-button
                      link
                      type="primary"
                      @click="() => preview(scope.row.fileKey, scope.row.fileName, 0)"
                    >
                      预览
                    </el-button>

                    <el-button
                      link
                      type="primary"
                      @click="() => preview(scope.row.fileKey, scope.row.fileName, 1)"
                    >
                      下载
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="日志" name="log">
              <el-table :data="logList" style="width: 100%">
                <el-table-column label="操作" prop="operateDetail" align="center" />
                <el-table-column label="操作人" prop="operateName" align="center" />
                <el-table-column label="操作时间" prop="operateTime" align="center" />
              </el-table>

              <!-- 分页组件 -->
              <div class="pagination-container" style="float: right">
                <el-pagination
                  background
                  layout="total, sizes, prev, pager, next, jumper"
                  :page-sizes="[10, 20, 50, 100]"
                  :current-page="logCurrentPage"
                  :page-size="logPageSize"
                  :total="logTotal"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </el-form>
  </div>
</template>

<style>
.form-item-text-align {
  text-align: left;
}
</style>
