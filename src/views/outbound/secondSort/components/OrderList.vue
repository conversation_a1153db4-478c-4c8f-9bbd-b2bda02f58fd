<script setup>
import { defineProps, computed, ref } from 'vue'
import { DocumentCopy } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { queryGridSku } from '@/api/outbound/secondSort'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  seedWallData: {
    type: Object,
    default: () => null,
  },
  settingData: {
    type: Object,
    default: () => ({}),
  },
})

// 弹窗相关数据
const dialogVisible = ref(false)
const selectedItem = ref(null)
const selectedIndex = ref(0)
const gridSkuDetail = ref([])

// 计算属性：获取格子列表
const gridList = computed(() => {
  return props.seedWallData?.seedWallDTO?.gridList || []
})

// 计算网格样式
const gridStyle = computed(() => {
  const style = {}
  const minItemWidth = 80
  if (props.settingData?.seedWallType === '1') {
    const mapNum = props.settingData.seedWallMapNum || 1
    const mapType = props.settingData.seedWallMapType

    if (mapType === 'column') {
      // 列模式 - 固定列数
      style.gridTemplateColumns = `repeat(${mapNum}, 1fr)`
    } else if (mapType === 'row') {
      // 行模式 - 固定行数
      const totalItems = gridList.value.length
      const itemsPerRow = Math.ceil(totalItems / mapNum)

      // 使用固定列数，让每行显示相同数量的卡片
      style.gridTemplateColumns = `repeat(${itemsPerRow}, 1fr)`
      style.gridAutoRows = 'minmax(80px, auto)'
    } else {
      // 未指定类型，使用自动填充
      style.gridTemplateColumns = `repeat(auto-fill, minmax(${minItemWidth}px, 1fr))`
    }
  } else {
    // 默认自动填充模式
    style.gridTemplateColumns = `repeat(auto-fill, minmax(${minItemWidth}px, 1fr))`
  }

  return style
})

// 序号点击事件
const handleNumberClick = async (item, index) => {
  console.log('点击了序号:', index + 1, '对应数据:', item)
  selectedItem.value = item
  selectedIndex.value = index

  // 调用 queryGridSku 接口获取格子详细信息
  try {
    if (item.gridSkuList && item.gridSkuList.length > 0) {
      const productSkuList = item.gridSkuList.map(sku => ({
        sku: sku.productSku,
        customerCode: item.customerCode
      }))

      const params = {
        productSkuList,
        whCode: props.seedWallData?.whCode || 'CA'
      }

      const res = await queryGridSku(params)
      if (res.code === 200 && res.data?.gridSkuInfoList) {
        gridSkuDetail.value = res.data.gridSkuInfoList
      } else {
        gridSkuDetail.value = []
        ElMessage.error(res.message || '获取格子详情失败')
      }
    } else {
      gridSkuDetail.value = []
    }
  } catch (error) {
    console.error('获取格子详情失败:', error)
    gridSkuDetail.value = []
    ElMessage.error('获取格子详情失败')
  }

  dialogVisible.value = true
}

// 关闭弹窗
const handleCloseDialog = () => {
  dialogVisible.value = false
  selectedItem.value = null
  selectedIndex.value = 0
}

// 打印面单
const handlePrint = () => {
  console.log('打印面单:', selectedItem.value)
  // TODO: 实现打印逻辑
}

// 计算属性：合并格子SKU信息和详细信息
const tableData = computed(() => {
  if (!selectedItem.value?.gridSkuList) return []

  return selectedItem.value.gridSkuList.map(gridSku => {
    // 从 gridSkuDetail 中找到对应的详细信息
    const detail = gridSkuDetail.value.find(detail => detail.productSku === gridSku.productSku)

    return {
      productSku: gridSku.productSku,
      barcode: detail?.barcode || gridSku.barcode || '',
      productName: detail?.productName || gridSku.productName || '',
      needPack: gridSku.needPack === 1 ? '是' : '否',
      recommendedPackMaterial: gridSku.recommendedPackMaterial?.code || '',
      recommendedPackMaterialName: gridSku.recommendedPackMaterial?.name || '',
      sortingProgress: `${gridSku.sortingQty}/${gridSku.pickQty}`
    }
  })
})

const copy = async (val) => {
  await navigator.clipboard.writeText(val)
  ElMessage.success('复制成功')
}
</script>

<template>
  <div class="profile-container" v-if="seedWallData" v-loading="loading">
    <!-- 上部分：进度信息和提示区域 -->
    <el-card class="top-card" shadow="never">
      <div class="progress-info">
        <span class="progress-item"
          >订单进度 <span class="greey">{{ seedWallData?.sortedOrderCount || 0 }}</span
          ><span class="info">/ {{ seedWallData?.orderCount || 0 }}</span></span
        >
        <span class="progress-item"
          >产品进度 <span class="greey">{{ seedWallData?.skuSortingQty || 0 }}</span
          ><span class="info">/ {{ seedWallData?.skuPickQty || 0 }}</span></span
        >
      </div>
      <div class="info-box">
        <div class="info-content">
          <img src="/scan.svg" />
          <label>请扫描商品，并根据提示将商品放置对应格子内</label>
        </div>
      </div>
    </el-card>

    <!-- 下部分：自适应内容展示区域 -->
    <div class="bottom-section">
      <div class="content-grid" :style="gridStyle">
        <el-card
          class="grid-item"
          v-for="(item, index) in gridList"
          :key="item.deliveryNo || index"
          shadow="hover"
          @click="handleNumberClick(item, index)"
        >
          <div class="card-content">
            <span class="item-number">{{ item.seedWallGridSerial || (index + 1) }}</span>
            <div class="progress-display">
              <span class="progress-text">{{ item.sortingQty }}/{{ item.pickQty }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
  <div class="defaultTips" v-else>
    <img src="/scan.svg" />
    <label>请扫描波次单号开始分拣</label>
  </div>

  <!-- 详情弹窗 -->
  <el-dialog v-model="dialogVisible" title="格子信息" width="900px" @close="handleCloseDialog">
    <!-- 上方信息区域 -->
    <el-row :gutter="20" class="info-section">
      <el-col :span="12">
        <span class="card-title">格子号 {{ selectedItem?.seedWallGridSerial || (selectedIndex + 1) }}</span>
      </el-col>
      <el-col :span="12">
        <span class="card-title"
          >出库单号 {{ selectedItem?.sourceNo || '' }}
          <el-icon @click="copy(selectedItem?.sourceNo || '')" class="copy-icon"> <DocumentCopy /> </el-icon
        ></span>
      </el-col>
    </el-row>

    <!-- 中间表格区域 -->
    <div class="table-section">
      <el-table :data="tableData" style="width: 100%" stripe>
        <el-table-column prop="productSku" label="SKU" width="150" show-overflow-tooltip />
        <el-table-column prop="barcode" label="产品条码" show-overflow-tooltip />
        <el-table-column prop="productName" label="产品名称" width="120" show-overflow-tooltip />
        <el-table-column prop="needPack" label="是否需要包材" width="120" align="center" />
        <el-table-column prop="recommendedPackMaterial" label="推荐包材编码" width="120" align="center" show-overflow-tooltip />
        <el-table-column prop="recommendedPackMaterialName" label="推荐包材名称" width="120" align="center" show-overflow-tooltip />
        <el-table-column prop="sortingProgress" label="已分拣/总量" width="100" align="center" show-overflow-tooltip />
      </el-table>
    </div>

    <!-- 底部按钮区域 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handlePrint">打印面单</el-button>
        <el-button @click="handleCloseDialog">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.profile-container {
  display: flex;
  flex-direction: column;
}

.top-card {
  margin-bottom: 20px;
}

.progress-info {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
  margin-bottom: 15px;
}

.progress-item {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}
.progress-item .greey {
  color: #28a864;
  font-weight: 600;
  font-size: 18px;
}
.progress-item .info {
  color: #0b1019;
  font-weight: 600;
  font-size: 18px;
}

.info-box {
  border: 1px solid #e6e8eb;
  border-radius: 4px;
  padding: 40px 20px;
  text-align: center;
  min-height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-content {
  text-align: center;
  color: #606266;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: row;
  gap: 16px;
}

.info-content img {
  width: 52px;
  height: 52px;
  opacity: 0.6;
}

.info-content label {
  font-size: 14px;
  color: #909399;
}

.bottom-section {
  flex: 1;
  overflow-y: auto;
}

.content-grid {
  display: grid;
  gap: 16px;
  padding: 10px 0;
}

.grid-item {
  transition: transform 0.2s ease;
  position: relative;
  cursor: pointer;
  min-height: 80px;
}

.grid-item:hover {
  transform: translateY(-2px);
}

.card-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;
}

.item-number {
  position: absolute;
  top: -10px;
  left: -10px;
  background: #409eff;
  color: white;
  font-size: 12px;
  font-weight: 600;
  width: 30px;
  height: 20px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  transition: all 0.2s ease;
}

.progress-display {
  text-align: center;
}

.progress-text {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

.defaultTips {
  text-align: center;
  color: #606266;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: row;
  gap: 16px;
}

.defaultTips img {
  width: 64px;
  height: 64px;
  opacity: 0.6;
}

.defaultTips label {
  font-size: 16px;
  color: #909399;
}

/* 弹窗样式 */
.info-section {
  margin-bottom: 20px;
}

.card-title {
  font-weight: 600;
  color: #303133;
}

.copy-icon {
  color: var(--el-color-primary);
  cursor: pointer;
  transition: color 0.3s;
}

.copy-icon:hover {
  color: var(--el-color-primary-dark-2);
}

.table-section {
  margin: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
