<script setup>
import { defineProps, computed, ref } from 'vue'
import { DocumentCopy } from '@element-plus/icons-vue'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  orderList: {
    type: Array,
    default: () => [],
  },
  orderData: {
    type: Array,
    default: () => [],
  },
  settingData: {
    type: Object,
    default: () => ({}),
  },
})

// 弹窗相关数据
const dialogVisible = ref(false)
const selectedItem = ref(null)
const selectedIndex = ref(0)

// 计算网格样式
const gridStyle = computed(() => {
  const style = {}
  const minItemWidth = 80
  if (props.settingData?.seedWallType === '1') {
    const mapNum = props.settingData.seedWallMapNum || 1
    const mapType = props.settingData.seedWallMapType

    if (mapType === 'column') {
      // 列模式 - 固定列数
      style.gridTemplateColumns = `repeat(${mapNum}, 1fr)`
    } else if (mapType === 'row') {
      // 行模式 - 固定行数
      const totalItems = props.orderData.length
      const itemsPerRow = Math.ceil(totalItems / mapNum)

      // 使用固定列数，让每行显示相同数量的卡片
      style.gridTemplateColumns = `repeat(${itemsPerRow}, 1fr)`
      style.gridAutoRows = 'minmax(80px, auto)'
    } else {
      // 未指定类型，使用自动填充
      style.gridTemplateColumns = `repeat(auto-fill, minmax(${minItemWidth}px, 1fr))`
    }
  } else {
    // 默认自动填充模式
    style.gridTemplateColumns = `repeat(auto-fill, minmax(${minItemWidth}px, 1fr))`
  }

  return style
})

// 序号点击事件
const handleNumberClick = (item, index) => {
  console.log('点击了序号:', index + 1, '对应数据:', item)
  selectedItem.value = item
  selectedIndex.value = index
  dialogVisible.value = true
  // TODO: 这里可以调用接口获取详细数据
}

// 关闭弹窗
const handleCloseDialog = () => {
  dialogVisible.value = false
  selectedItem.value = null
  selectedIndex.value = 0
}

// 打印面单
const handlePrint = () => {
  console.log('打印面单:', selectedItem.value)
  // TODO: 实现打印逻辑
}

// 模拟表格数据（实际应该从接口获取）
const tableData = ref([
  { id: 1, productName: '商品A', sku: 'SKU001', quantity: 2, status: '待分拣' },
  { id: 2, productName: '商品B', sku: 'SKU002', quantity: 1, status: '已分拣' },
  { id: 3, productName: '商品C', sku: 'SKU003', quantity: 3, status: '待分拣' },
])

const copy = async (val) => {
  await navigator.clipboard.writeText(val)
  ElMessage.success('复制成功')
}
</script>

<template>
  <div class="profile-container" v-if="1" v-loading="loading">
    <!-- 上部分：进度信息和提示区域 -->
    <el-card class="top-card" shadow="never">
      <div class="progress-info">
        <span class="progress-item"
          >订单进度 <span class="greey">{{ orderList.length }}</span
          ><span class="info">/ {{ orderList.length }}</span></span
        >
        <span class="progress-item"
          >产品进度 <span class="greey">{{ orderData.length }}</span
          ><span class="info">/ {{ orderData.length }}</span></span
        >
      </div>
      <div class="info-box">
        <div class="info-content">
          <img src="/scan.svg" />
          <label>请扫描商品，并根据提示将商品放置对应格子内</label>
        </div>
      </div>
    </el-card>

    <!-- 下部分：自适应内容展示区域 -->
    <div class="bottom-section">
      <div class="content-grid" :style="gridStyle">
        <el-card
          class="grid-item"
          v-for="(item, index) in orderData"
          :key="item.id || index"
          shadow="hover"
          @click="handleNumberClick(item, index)"
        >
          <div class="card-content">
            <span class="item-number">{{ index + 1 }}</span>
            <div class="progress-display">
              <span class="progress-text">{{ index + 1 }}/{{ orderData.length }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
  <div class="defaultTips" v-else>
    <img src="/scan.svg" />
    <label>请扫描波次单号开始分拣</label>
  </div>

  <!-- 详情弹窗 -->
  <el-dialog v-model="dialogVisible" title="格子信息" width="900px" @close="handleCloseDialog">
    <!-- 上方信息区域 -->
    <el-row :gutter="20" class="info-section">
      <el-col :span="12">
        <span class="card-title">格子号 {{ selectedIndex + 1 }}</span>
      </el-col>
      <el-col :span="12">
        <span class="card-title"
          >出库单号 {{ 2435436547564578 }}
          <el-icon @click="copy(2435436547564578)" class="copy-icon"> <DocumentCopy /> </el-icon
        ></span>
      </el-col>
    </el-row>

    <!-- 中间表格区域 -->
    <div class="table-section">
      <el-table :data="tableData" style="width: 100%" stripe>
        <el-table-column prop="productName"  label="SKU" width="100" show-overflow-tooltip />
        <el-table-column prop="sku" label="产品条码" show-overflow-tooltip />
        <el-table-column prop="quantity" label="产品名称" width="100" align="center" show-overflow-tooltip />
        <el-table-column prop="status" label="是否需要包材" width="120" align="center" />
        <el-table-column prop="status" label="推荐包材编码" width="120" align="center" show-overflow-tooltip />
        <el-table-column prop="status" label="推荐包材名称" width="120" align="center" show-overflow-tooltip>
          <template #default="scope">
            <el-tag :type="scope.row.status === '已分拣' ? 'success' : 'warning'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="已分拣/总量" width="100" align="center" show-overflow-tooltip />
      </el-table>
    </div>

    <!-- 底部按钮区域 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handlePrint">打印面单</el-button>
        <el-button @click="handleCloseDialog">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.profile-container {
  display: flex;
  flex-direction: column;
}

.top-card {
  margin-bottom: 20px;
}

.progress-info {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
  margin-bottom: 15px;
}

.progress-item {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}
.progress-item .greey {
  color: #28a864;
  font-weight: 600;
  font-size: 18px;
}
.progress-item .info {
  color: #0b1019;
  font-weight: 600;
  font-size: 18px;
}

.info-box {
  border: 1px solid #e6e8eb;
  border-radius: 4px;
  padding: 40px 20px;
  text-align: center;
  min-height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-content {
  text-align: center;
  color: #606266;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: row;
  gap: 16px;
}

.info-content img {
  width: 52px;
  height: 52px;
  opacity: 0.6;
}

.info-content label {
  font-size: 14px;
  color: #909399;
}

.bottom-section {
  flex: 1;
  overflow-y: auto;
}

.content-grid {
  display: grid;
  gap: 16px;
  padding: 10px 0;
}

.grid-item {
  transition: transform 0.2s ease;
  position: relative;
  cursor: pointer;
  min-height: 80px;
}

.grid-item:hover {
  transform: translateY(-2px);
}

.card-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;
}

.item-number {
  position: absolute;
  top: -10px;
  left: -10px;
  background: #409eff;
  color: white;
  font-size: 12px;
  font-weight: 600;
  width: 30px;
  height: 20px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  transition: all 0.2s ease;
}

.progress-display {
  text-align: center;
}

.progress-text {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

.defaultTips {
  text-align: center;
  color: #606266;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: row;
  gap: 16px;
}

.defaultTips img {
  width: 64px;
  height: 64px;
  opacity: 0.6;
}

.defaultTips label {
  font-size: 16px;
  color: #909399;
}

/* 弹窗样式 */
.info-section {
  margin-bottom: 20px;
}

.card-title {
  font-weight: 600;
  color: #303133;
}

.copy-icon {
  color: var(--el-color-primary);
  cursor: pointer;
  transition: color 0.3s;
}

.copy-icon:hover {
  color: var(--el-color-primary-dark-2);
}

.table-section {
  margin: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
